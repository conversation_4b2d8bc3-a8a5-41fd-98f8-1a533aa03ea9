<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户昵称" prop="userNickName">
        <el-input v-model="queryParams.userNickName" placeholder="请输入用户昵称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="用户手机" prop="userPhone">
        <el-input v-model="queryParams.userPhone" placeholder="请输入用户手机号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="店铺名称" prop="shopName">
        <el-input v-model="queryParams.shopName" placeholder="请输入店铺名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="法人姓名" prop="legalPerson">
        <el-input v-model="queryParams.legalPerson" placeholder="请输入法人姓名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="申请状态" prop="applicationStatus">
        <el-select v-model="queryParams.applicationStatus" placeholder="请选择申请状态" clearable>
          <el-option label="待审核" value="0" />
          <el-option label="审核通过" value="1" />
          <el-option label="审核拒绝" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-check" size="mini" :disabled="multiple"
          @click="handleBatchAudit('1')" v-hasPermi="['fuguang:merchant:audit']">批量通过</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-close" size="mini" :disabled="multiple"
          @click="handleBatchAudit('2')" v-hasPermi="['fuguang:merchant:audit']">批量拒绝</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['fuguang:merchant:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border :data="merchantList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="申请ID" align="center" prop="applicationId" width="80" />
      <el-table-column label="用户信息" align="center" width="120">
        <template slot-scope="scope">
          <div>{{ scope.row.userNickName }}</div>
          <div style="color: #909399; font-size: 12px;">{{ scope.row.userPhone }}</div>
        </template>
      </el-table-column>
      <el-table-column label="店铺名称" align="center" prop="shopName" width="120" />
      <el-table-column label="店铺地址" align="center" prop="shopAddress" width="200" show-overflow-tooltip />
      <el-table-column label="法人信息" align="center" width="120">
        <template slot-scope="scope">
          <div>{{ scope.row.legalPerson }}</div>
          <div style="color: #909399; font-size: 12px;">{{ scope.row.legalPhone }}</div>
        </template>
      </el-table-column>
      <el-table-column label="申请状态" align="center" prop="applicationStatus" width="100">
        <template slot-scope="scope">
          <dict-tag :options="statusOptions" :value="scope.row.applicationStatus" />
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核人" align="center" prop="auditBy" width="100" />
      <el-table-column label="审核时间" align="center" prop="auditTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)"
            v-hasPermi="['fuguang:merchant:query']">查看</el-button>
          <el-button v-if="scope.row.applicationStatus === '0'" size="mini" type="text" icon="el-icon-check"
            @click="handleAudit(scope.row, '1')" v-hasPermi="['fuguang:merchant:audit']">通过</el-button>
          <el-button v-if="scope.row.applicationStatus === '0'" size="mini" type="text" icon="el-icon-close"
            @click="handleAudit(scope.row, '2')" v-hasPermi="['fuguang:merchant:audit']">拒绝</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 查看详情对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户昵称">
              <span>{{ form.userNickName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户手机">
              <span>{{ form.userPhone }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="店铺名称">
              <span>{{ form.shopName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="经营范围">
              <span>{{ form.businessScope }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="店铺地址">
              <span>{{ form.shopAddress }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="法人姓名">
              <span>{{ form.legalPerson }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="法人身份证">
              <span>{{ form.legalIdCard }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="法人电话">
              <span>{{ form.legalPhone }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请状态">
              <dict-tag :options="statusOptions" :value="form.applicationStatus" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="支付宝账号">
              <span>{{ form.alipayAccount || '未填写' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款人姓名">
              <span>{{ form.alipayName || '未填写' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="营业执照">
              <el-image v-if="form.businessLicense" :src="form.businessLicense" style="width: 200px; height: 150px;"
                fit="contain" :preview-src-list="[form.businessLicense]">
              </el-image>
              <span v-else>未上传</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.auditBy">
          <el-col :span="12">
            <el-form-item label="审核人">
              <span>{{ form.auditBy }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审核时间">
              <span>{{ parseTime(form.auditTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.auditRemark">
          <el-col :span="24">
            <el-form-item label="审核备注">
              <span>{{ form.auditRemark }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
        <el-button v-if="form.applicationStatus === '0'" type="success" @click="handleAudit(form, '1')"
          v-hasPermi="['fuguang:merchant:audit']">审核通过</el-button>
        <el-button v-if="form.applicationStatus === '0'" type="danger" @click="handleAudit(form, '2')"
          v-hasPermi="['fuguang:merchant:audit']">审核拒绝</el-button>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog :title="auditTitle" :visible.sync="auditOpen" width="500px" append-to-body>
      <el-form ref="auditForm" :model="auditForm" label-width="80px">
        <el-form-item label="审核结果">
          <el-tag v-if="auditForm.applicationStatus === '1'" type="success">审核通过</el-tag>
          <el-tag v-else type="danger">审核拒绝</el-tag>
        </el-form-item>
        <el-form-item label="审核备注">
          <el-input v-model="auditForm.auditRemark" type="textarea" :rows="4" placeholder="请输入审核备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditOpen = false">取 消</el-button>
        <el-button type="primary" @click="submitAudit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMerchantApplication, getMerchantApplication, auditMerchantApplication, batchAuditMerchantApplication } from "@/api/fuguang/merchant";

export default {
  name: "MerchantApplication",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商家申请表格数据
      merchantList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 审核弹出层标题
      auditTitle: "",
      // 是否显示审核弹出层
      auditOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userNickName: null,
        userPhone: null,
        shopName: null,
        legalPerson: null,
        applicationStatus: null
      },
      // 表单参数
      form: {},
      // 审核表单参数
      auditForm: {},
      // 状态选项
      statusOptions: [
        { label: "待审核", value: "0" },
        { label: "审核通过", value: "1" },
        { label: "审核拒绝", value: "2" }
      ]
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询商家申请列表 */
    getList() {
      this.loading = true;
      listMerchantApplication(this.queryParams).then(response => {
        this.merchantList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        applicationId: null,
        userId: null,
        businessLicense: null,
        shopName: null,
        shopAddress: null,
        shopLongitude: null,
        shopLatitude: null,
        legalPerson: null,
        legalIdCard: null,
        legalPhone: null,
        businessScope: null,
        alipayAccount: null,
        alipayName: null,
        applicationStatus: null,
        auditBy: null,
        auditTime: null,
        auditRemark: null,
        status: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.applicationId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const applicationId = row.applicationId || this.ids;
      getMerchantApplication(applicationId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "商家申请详情";
      });
    },
    /** 审核按钮操作 */
    handleAudit(row, status) {
      this.auditForm = {
        applicationId: row.applicationId,
        applicationStatus: status,
        auditRemark: ""
      };
      this.auditTitle = status === '1' ? '审核通过' : '审核拒绝';
      this.auditOpen = true;
    },
    /** 提交审核 */
    submitAudit() {
      auditMerchantApplication(this.auditForm).then(response => {
        this.$modal.msgSuccess(response.msg);
        this.auditOpen = false;
        this.open = false;
        this.getList();
      });
    },
    /** 批量审核操作 */
    handleBatchAudit(status) {
      const applicationIds = this.ids;
      const statusText = status === '1' ? '通过' : '拒绝';
      this.$prompt('请输入审核备注', '批量审核' + statusText, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'textarea'
      }).then(({ value }) => {
        const data = {
          applicationIds: applicationIds,
          applicationStatus: status,
          auditRemark: value || ''
        };
        batchAuditMerchantApplication(data).then(response => {
          this.$modal.msgSuccess(response.msg);
          this.getList();
        });
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('fuguang/merchant/export', {
        ...this.queryParams
      }, `merchant_application_${new Date().getTime()}.xlsx`);
    }
  }
};
</script>
